{"formatVersion": 1, "database": {"version": 1, "identityHash": "934fd7e4b076ae2310e1e5a25531f827", "entities": [{"tableName": "barbers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`uuid` TEXT NOT NULL, `name` TEXT NOT NULL, `commission_rate` REAL NOT NULL, `phone_number` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, PRIMARY KEY(`uuid`))", "fields": [{"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "commissionRate", "columnName": "commission_rate", "affinity": "REAL", "notNull": true}, {"fieldPath": "phoneNumber", "columnName": "phone_number", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["uuid"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '934fd7e4b076ae2310e1e5a25531f827')"]}}