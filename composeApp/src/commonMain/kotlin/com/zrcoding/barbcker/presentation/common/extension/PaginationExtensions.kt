package com.zrcoding.barbcker.presentation.common.extension

import androidx.compose.runtime.Composable
import app.cash.paging.LoadStateError
import app.cash.paging.LoadStateLoading
import app.cash.paging.LoadStateNotLoading
import app.cash.paging.LoadStates
import app.cash.paging.PagingData
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import kotlinx.coroutines.flow.flowOf

/**
 * Extension function to check if the `LazyPagingItems` is in a loading state.
 *
 * This function determines whether the `loadState.refresh` is currently an instance of `LoadState.Loading`.
 *
 * @return `true` if the list is in a loading state, otherwise `false`.
 */
fun <T : Any> LazyPagingItems<T>.isLoading() = loadState.refresh is LoadStateLoading

/**
 * Extension function to check if the `LazyPagingItems` is empty.
 *
 * This function determines if the pagination has reached the end (`endOfPaginationReached`)
 * and there are no items in the list (`itemCount == 0`).
 *
 * @return `true` if the list is empty and pagination has ended, otherwise `false`.
 */
fun <T : Any> LazyPagingItems<T>.isEmpty() =
    loadState.append.endOfPaginationReached && itemCount == 0

/**
 * Creates an empty [LazyPagingItems] instance for use in a previews.
 *
 * This function is useful to preview [isEmpty] case.
 * It returns a [LazyPagingItems] instance that contains no data
 * and has load states set to `NotLoading` with `endOfPaginationReached` set to `true`.
 *
 * @receiver [PagingData] to extend with the ability to create an empty [LazyPagingItems].
 * @return A [LazyPagingItems] instance with no data and predefined `LoadStates`.
 */
@Composable
fun <T : Any> emptyLazyPagingItems(): LazyPagingItems<T> {
    return flowOf(
        PagingData.from<T>(
            data = emptyList(),
            sourceLoadStates = LoadStates(
                refresh = LoadStateNotLoading(true),
                append = LoadStateNotLoading(true),
                prepend = LoadStateNotLoading(true),
            )
        )
    ).collectAsLazyPagingItems()
}

/**
 * Extension function to check if the `LazyPagingItems` is in an error state.
 *
 * This function evaluates whether the refresh state of the list is an instance of `LoadState.Error`
 * and there are no items in the list (`itemCount == 0`).
 *
 * @return `true` if the list is in an error state with no items, otherwise `false`.
 */
fun <T : Any> LazyPagingItems<T>.isError() = loadState.refresh is LoadStateError && itemCount == 0

/**
 * Creates an error [LazyPagingItems] instance for use in a previews.
 *
 * This function is useful to preview [isError] case.
 * It returns a [LazyPagingItems] instance that contains exception in refresh loadState.
 * and has load states set to `NotLoading` with `endOfPaginationReached` set to `true`.
 *
 * @receiver [PagingData] to extend with the ability to create an empty [LazyPagingItems].
 * @return A [LazyPagingItems] instance with `sourceLoadStates.refresh = LoadState.Error`.
 */
@Composable
fun <T : Any> errorLazyPagingItems(): LazyPagingItems<T> {
    return flowOf(
        PagingData.from<T>(
            data = emptyList(),
            sourceLoadStates = LoadStates(
                refresh = LoadStateError(Throwable("Fake error message")),
                append = LoadStateError(Throwable("Fake error message")),
                prepend = LoadStateError(Throwable("Fake error message"))
            )
        )
    ).collectAsLazyPagingItems()
}

/**
 * Extension function to retrieve the error from the `LazyPagingItems` refresh state, if any.
 *
 * This function checks if the `loadState.refresh` is an instance of `LoadState.Error`.
 * If it is, it extracts and returns the associated `Throwable` error.
 *
 * @return The `Throwable` error if the refresh state is `LoadState.Error`, otherwise `null`.
 */
fun <T : Any> LazyPagingItems<T>.getError(): Throwable? =
    (loadState.refresh as? LoadStateError)?.error