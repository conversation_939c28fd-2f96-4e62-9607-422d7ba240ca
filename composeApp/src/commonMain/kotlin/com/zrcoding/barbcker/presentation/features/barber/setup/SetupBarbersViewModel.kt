package com.zrcoding.barbcker.presentation.features.barber.setup

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.cash.paging.cachedIn
import com.zrcoding.barbcker.domain.repositories.BarberRepository

class SetupBarbersViewModel(
    private val barberRepository: BarberRepository
) : ViewModel() {

    val barbers = barberRepository.observeAll().cachedIn(viewModelScope)
}