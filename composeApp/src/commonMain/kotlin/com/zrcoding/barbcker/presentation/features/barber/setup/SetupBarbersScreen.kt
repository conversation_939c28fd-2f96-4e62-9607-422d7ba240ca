package com.zrcoding.barbcker.presentation.features.barber.setup

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.img_barber1
import barbcker.composeapp.generated.resources.img_barbers
import barbcker.composeapp.generated.resources.setup_barbers_add_barber_button
import barbcker.composeapp.generated.resources.setup_barbers_commission_rate
import barbcker.composeapp.generated.resources.setup_barbers_empty_state_description
import barbcker.composeapp.generated.resources.setup_barbers_title
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.common.extension.isEmpty
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.barber.upsert.UpsertBarberBottomSheet
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel
import kotlin.uuid.ExperimentalUuidApi

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SetupBarbersRoute(
    navigateBack: () -> Unit,
    viewModel: SetupBarbersViewModel = koinViewModel()
) {
    val scope = rememberCoroutineScope()
    val viewState = viewModel.barbers.collectAsLazyPagingItems()
    var showAddBarberBottomSheet by remember { mutableStateOf(false) }
    var clickedBarberId by remember { mutableStateOf<String?>(null) }
    SetupBarbersScreen(
        lazyPagingItems = viewState,
        onNavigateBack = navigateBack,
        onBarberClicked = {
            clickedBarberId = it.uuid
            showAddBarberBottomSheet = true
        },
        onAddBarberClicked = {
            showAddBarberBottomSheet = true
        },
    )
    if (showAddBarberBottomSheet) {
        val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
        ModalBottomSheet(
            sheetState = sheetState,
            onDismissRequest = {
                scope.launch { sheetState.hide() }.invokeOnCompletion {
                    showAddBarberBottomSheet = false
                    clickedBarberId = null
                }
            }
        ) {
            UpsertBarberBottomSheet(
                barberId = clickedBarberId,
                onSuccess = {
                    scope.launch { sheetState.hide() }.invokeOnCompletion {
                        showAddBarberBottomSheet = false
                        clickedBarberId = null
                    }
                }
            )
        }
    }
}

@OptIn(ExperimentalUuidApi::class)
@Composable
private fun SetupBarbersScreen(
    lazyPagingItems: LazyPagingItems<Barber>,
    onNavigateBack: () -> Unit,
    onBarberClicked: (Barber) -> Unit,
    onAddBarberClicked: () -> Unit,
) {
    Scaffold(
        modifier = Modifier.fillMaxSize().safeDrawingPadding(),
        topBar = {
            BcTopAppBar(
                onNavigationIconClicked = onNavigateBack,
                title = stringResource(Res.string.setup_barbers_title)
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier.fillMaxSize()
                .padding(paddingValues)
                .padding(vertical = MaterialTheme.dimension.big)
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
        ) {
            when {
                lazyPagingItems.isEmpty() -> Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
                ) {
                    Text(
                        text = stringResource(Res.string.setup_barbers_empty_state_description),
                        style = MaterialTheme.typography.titleMedium,
                    )
                    Image(
                        modifier = Modifier.fillMaxWidth(),
                        painter = painterResource(Res.drawable.img_barbers),
                        contentDescription = null,
                        contentScale = ContentScale.Crop
                    )
                }

                else -> LazyColumn {
                    items(
                        count = lazyPagingItems.itemCount,
                        key = { index -> lazyPagingItems[index]?.uuid }
                    ) { index ->
                        lazyPagingItems[index]?.let { barber ->
                            BarberItem(
                                name = barber.name,
                                commissionRate = barber.commissionRate,
                                phoneNumber = barber.phoneNumber,
                                onClick = { onBarberClicked(barber) }
                            )
                        }
                    }
                }
            }
            BcPrimaryButton(
                modifier = Modifier.align(Alignment.BottomEnd),
                text = stringResource(Res.string.setup_barbers_add_barber_button),
                leadingIcon = {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimension.bigger),
                        imageVector = Icons.Filled.PersonAddAlt1,
                        contentDescription = null
                    )
                },
                onClick = onAddBarberClicked
            )
        }
    }
}

@Composable
fun BarberItem(
    name: String,
    commissionRate: Double,
    phoneNumber: String,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(
                vertical = MaterialTheme.dimension.medium,
                horizontal = MaterialTheme.dimension.small
            )
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
    ) {
        Image(
            modifier = Modifier.size(56.dp),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Barber avatar"
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(text = name, style = MaterialTheme.typography.bodyLarge)
                Text(text = phoneNumber, style = MaterialTheme.typography.bodyLarge)
            }
            Text(
                text = stringResource(
                    Res.string.setup_barbers_commission_rate,
                    commissionRate
                ),
                style = MaterialTheme.typography.labelLarge
            )
        }
    }
}