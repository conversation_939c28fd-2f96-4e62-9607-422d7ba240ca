package com.zrcoding.barbcker.presentation.features.barber.upsert

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_can_be_changed_later
import barbcker.composeapp.generated.resources.common_save
import barbcker.composeapp.generated.resources.upsert_barber_commission_rate_label
import barbcker.composeapp.generated.resources.upsert_barber_name_label
import barbcker.composeapp.generated.resources.upsert_barber_phone_number_label
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTextField
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UpsertBarberBottomSheet(
    barberId: String?,
    onSuccess: () -> Unit,
    viewModel: UpsertBarberViewModel = koinViewModel(),
) {
    LaunchedEffect(barberId) {
        barberId?.let { viewModel.getBarberForEdit(it) }
    }
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = onSuccess
    ) {
        UpsertBarberScreen(
            viewState = viewState,
            onNameChanged = viewModel::onNameChanged,
            onCommissionRateChanged = viewModel::onCommissionRateChanged,
            onPhoneNumberChanged = viewModel::onPhoneNumberChanged,
            onSubmit = viewModel::onSubmit,
        )
    }
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            onSuccess()
        }
    }
}

@Composable
private fun UpsertBarberScreen(
    viewState: UpsertBarberViewState,
    onNameChanged: (String) -> Unit,
    onCommissionRateChanged: (Double) -> Unit,
    onPhoneNumberChanged: (String) -> Unit,
    onSubmit: () -> Unit,
) {
    val focusManager = LocalFocusManager.current
    Column(
        modifier = Modifier.padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.big)
    ) {
        BcTextField(
            modifier = Modifier.fillMaxWidth(),
            value = viewState.name,
            onValueChanged = onNameChanged,
            title = Res.string.upsert_barber_name_label,
            error = viewState.nameError?.let { stringResource(it) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next,
            )
        )
        BcTextField(
            modifier = Modifier.fillMaxWidth(),
            value = viewState.commissionRate.toString(),
            onValueChanged = { onCommissionRateChanged(it.toDoubleOrNull() ?: 0.0) },
            title = Res.string.upsert_barber_commission_rate_label,
            error = viewState.commissionRateError?.let { stringResource(it) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next,
            )
        )
        BcTextField(
            modifier = Modifier.fillMaxWidth(),
            value = viewState.phoneNumber,
            onValueChanged = onPhoneNumberChanged,
            title = Res.string.upsert_barber_phone_number_label,
            error = viewState.phoneNumberError?.let { stringResource(it) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Phone,
                imeAction = ImeAction.Done,
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                    onSubmit()
                }
            )
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(Res.string.common_can_be_changed_later),
                style = MaterialTheme.typography.labelMedium,
            )
            BcPrimaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(Res.string.common_save),
                onClick = {
                    focusManager.clearFocus()
                    onSubmit()
                },
            )
        }
    }
}