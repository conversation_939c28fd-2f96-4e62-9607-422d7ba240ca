package com.zrcoding.barbcker.domain.repositories

import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.Barber
import kotlinx.coroutines.flow.Flow

interface BarberRepository {
    suspend fun insert(name: String, commissionRate: Double, phoneNumber: String)

    suspend fun update(uuid: String, name: String, commissionRate: Double, phoneNumber: String)

    suspend fun getOne(uuid: String): Barber?

    fun observeAll(): Flow<PagingData<Barber>>
}