package com.zrcoding.barbcker.data.mappers

import com.zrcoding.barbcker.data.database.entities.BarberEntity
import com.zrcoding.barbcker.domain.models.Barber
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

fun BarberEntity.toBarber(): <PERSON> {
    return Barber(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber
    )
}

@OptIn(ExperimentalTime::class)
fun Barber.toBarberEntity(): BarberEntity {
    return BarberEntity(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber,
        createdAt = Clock.System.now().toEpochMilliseconds(),
        updatedAt = Clock.System.now().toEpochMilliseconds()
    )
}