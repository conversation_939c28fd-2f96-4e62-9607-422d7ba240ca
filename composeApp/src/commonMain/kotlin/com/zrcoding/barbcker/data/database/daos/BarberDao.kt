package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.zrcoding.barbcker.data.database.entities.BarberEntity

@Dao
interface BarberDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOne(barber: BarberEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMany(barbers: List<BarberEntity>)

    @Query("UPDATE barbers SET name = :name, commission_rate = :commissionRate, phone_number = :phoneNumber WHERE uuid = :uuid")
    suspend fun updateOne(uuid: String, name: String, commissionRate: Double, phoneNumber: String)

    @Query("SELECT * FROM barbers WHERE uuid = :uuid")
    suspend fun getOne(uuid: String): BarberEntity?

    @Query("SELECT * FROM barbers LIMIT :limit OFFSET :offset")
    fun getMany(limit: Long, offset: Long): List<BarberEntity>

    @Query("SELECT COUNT(*) FROM barbers")
    fun getCount(): Int

    @Query("DELETE FROM barbers")
    suspend fun clear()
}


