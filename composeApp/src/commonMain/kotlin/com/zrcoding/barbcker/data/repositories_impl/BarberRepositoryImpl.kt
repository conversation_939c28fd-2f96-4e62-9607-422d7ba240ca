package com.zrcoding.barbcker.data.repositories_impl

import app.cash.paging.Pager
import app.cash.paging.PagingConfig
import app.cash.paging.PagingData
import app.cash.paging.PagingSource
import app.cash.paging.PagingState
import app.cash.paging.map
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.mappers.toBarber
import com.zrcoding.barbcker.data.mappers.toBarberEntity
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlin.math.max
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class BarberRepositoryImpl(
    private val appDatabase: AppDatabase
) : BarberRepository {
    @OptIn(ExperimentalUuidApi::class)
    override suspend fun insert(name: String, commissionRate: Double, phoneNumber: String) {
        val barber = Barber(
            uuid = Uuid.random().toString(),
            name = name,
            commissionRate = commissionRate,
            phoneNumber = phoneNumber
        )
        appDatabase.barberDao().insertOne(barber.toBarberEntity())
    }

    override suspend fun update(
        uuid: String,
        name: String,
        commissionRate: Double,
        phoneNumber: String
    ) {
        appDatabase.barberDao().updateOne(
            uuid = uuid,
            name = name,
            commissionRate = commissionRate,
            phoneNumber = phoneNumber
        )
    }

    override suspend fun getOne(uuid: String): Barber? {
        return appDatabase.barberDao().getOne(uuid)?.toBarber()
    }

    override fun observeAll(): Flow<PagingData<Barber>> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                BarbersPagingSource(
                    queryProvider = { pageSize, offset ->
                        appDatabase.barberDao().getMany(pageSize, offset)
                    },
                    countQuery = {
                        appDatabase.barberDao().getCount()
                    }
                )
            }
        ).flow.map { pagingData ->
            pagingData.map { it.toBarber() }
        }
    }
}

//  PagingSource for SQLDelight
class BarbersPagingSource<T : Any>(
    private val queryProvider: (Long, Long) -> List<T>,
    private val countQuery: () -> Int
) : PagingSource<Int, T>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, T> {
        return try {
            val pageNumber = params.key ?: 0
            val pageSize = params.loadSize
            val offset = (pageNumber * pageSize).toLong()

            val items = queryProvider(pageSize.toLong(), offset)
            val totalItems = countQuery()
            val itemsBefore = pageNumber * pageSize
            val itemsAfter = max(0, totalItems - itemsBefore - items.size)

            LoadResult.Page(
                data = items,
                prevKey = if (pageNumber > 0) pageNumber - 1 else null,
                nextKey = if (items.isNotEmpty() && items.size == pageSize) pageNumber + 1 else null,
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, T>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }
}