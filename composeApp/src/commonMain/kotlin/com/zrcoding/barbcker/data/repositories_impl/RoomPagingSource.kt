package com.zrcoding.barbcker.data.repositories_impl

import app.cash.paging.PagingSource
import app.cash.paging.PagingState
import kotlin.math.max

/**
 * A generic PagingSource implementation for Room database entities.
 *
 * This class can be reused for any entity type that needs pagination support.
 *
 * @param T The entity type
 * @param queryProvider A suspend function that takes (limit, offset) and returns a list of entities
 * @param countQuery A suspend function that returns the total count of entities
 *
 * Example usage:
 * ```
 * RoomPagingSource(
 *     queryProvider = { limit, offset -> dao.getMany(limit, offset) },
 *     countQuery = { dao.getCount() }
 * )
 * ```
 */
class RoomPagingSource<T : Any>(
    private val queryProvider: suspend (limit: Long, offset: Long) -> List<T>,
    private val countQuery: suspend () -> Int
) : PagingSource<Int, T>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, T> {
        return try {
            val pageNumber = params.key ?: 0
            val pageSize = params.loadSize

            // Validate inputs
            if (pageNumber < 0 || pageSize <= 0) {
                return LoadResult.Error(IllegalArgumentException("Invalid page parameters: pageNumber=$pageNumber, pageSize=$pageSize"))
            }

            val offset = (pageNumber * pageSize).toLong()

            val items = queryProvider(pageSize.toLong(), offset)
            val totalItems = countQuery()
            val itemsBefore = pageNumber * pageSize
            val itemsAfter = max(0, totalItems - itemsBefore - items.size)

            // More robust nextKey calculation
            val nextKey = if (itemsAfter > 0) pageNumber + 1 else null

            LoadResult.Page(
                data = items,
                prevKey = if (pageNumber > 0) pageNumber - 1 else null,
                nextKey = nextKey,
                itemsBefore = itemsBefore,
                itemsAfter = itemsAfter
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, T>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}